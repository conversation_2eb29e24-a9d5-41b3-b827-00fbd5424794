/*
cd ~/main/eterna/rust/ && \
cargo run --bin parse-daemon -- \
--source-log /FOO/BAR/BAZ/logs/2025-06-13--Fri.log \
--log-date 2020-01-02 \
--already-accomplished Sensor1 Sensor2 \
--sensor-list-of-names Sensor1 Sensor2 Sensor3 \
--sensor-list-of-names-and-addresses *********** Sensor1 *********** Sensor2 \
--sensor-dict-of-addresses-and-names '{"***********": "Sensor1", "***********": "Sensor2"}'
*/

use std::collections::HashMap;
use std::fs::File;
use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>uf<PERSON><PERSON><PERSON>};

use clap::Parser;
use rayon::prelude::*;
use serde_json;

use eterna::utils_classes::{
    Daemon<PERSON>onfig,
    DaemonParser,
    MYSQLConfig,
};
use eterna::utils_parsers::{parse_ln, ConfigType};

#[derive(Parser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "source-log")]
    source_log: String,
    // /FOO/BAR/BAZ/logs/2025-06-13--Fri.log

    #[arg(long = "log-date")]
    log_date: String,
    // 2020-01-02

    #[arg(long = "already-accomplished", num_args = 0..)]
    already_accomplished: Vec<String>,
    // [] OR ["Sensor-1", "Sensor-2"]

    #[arg(long = "sensor-list-of-names", num_args = 1..)]
    sensor_list_of_names: Vec<String>,
    // ["Sensor-1", "Sensor-2", "Sensor-3"]

    #[arg(long = "sensor-list-of-names-and-addresses", num_args = 1..)]
    sensor_list_of_names_and_addresses: Vec<String>,
    // ["Sensor-1", "***********", "Sensor-2", "***********"]

    #[arg(long = "sensor-dict-of-addresses-and-names")]
    sensor_dict_of_addresses_and_names: String,
    // "{\"***********\": \"Sensor-1\", \"***********\": \"Sensor-2\"}"
}

fn main() {
    let args = Args::parse();

    // println!("Source log: {:?}", args.source_log);
    // println!("Log date: {:?}", args.log_date);
    // println!("Already accomplished: {:?}", args.already_accomplished);
    // println!("Sensor list of names: {:?}", args.sensor_list_of_names);
    // println!("Sensor list of names and addresses: {:?}", args.sensor_list_of_names_and_addresses);
    // println!("Sensor dict of addresses and names: {:?}", args.sensor_dict_of_addresses_and_names);

    // string -> dict
    let sensor_dict_of_addresses_and_names: HashMap<String, String> =
        serde_json::from_str(&args.sensor_dict_of_addresses_and_names)
            .expect("Failed to parse sensor_dict_of_addresses_and_names");

    // create dictionary of instances
    let mut sensor_names_and_instances: HashMap<String, DaemonParser> = args.sensor_list_of_names
        .iter()
        .map(|s_n| {
            (
                s_n.clone(),
                DaemonParser::new(
                    DaemonConfig::SLUG.value_string(),
                    args.log_date.clone(),
                    s_n.clone(),
                )
            )
        })
        .collect();

    // Read file and process lines in parallel
    let file = File::open(&args.source_log)
        .expect("Failed to open source log file");
    let reader = BufReader::new(file);

    // Collect all lines into a vector for parallel processing
    let lines: Vec<String> = reader.lines()
        .collect::<Result<Vec<_>, _>>()
        .expect("Failed to read lines from file");

    println!("parsing...");

    // Process lines in parallel using rayon
    let valid_lines: Vec<(Option<String>, Option<Vec<String>>)> = lines
        .par_iter()
        .map(|line| {
            let trimmed_line = line.trim();
            let (sensor_name, parsed_ln) = parse_ln(
                trimmed_line,
                ConfigType::Daemon,
                &args.sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            // Check if sensor_name is in already_accomplished
            if let Some(ref name) = sensor_name {
                if args.already_accomplished.contains(name) {
                    return (None, None);
                }
            }

            (sensor_name, parsed_ln)
        })
        .collect();

    // Process results and add to sensor instances
    for (sensor_name, parsed_ln) in valid_lines {
        if let (Some(sensor_name), Some(parsed_ln)) = (sensor_name, parsed_ln) {
            if let Some(instance) = sensor_names_and_instances.get_mut(&sensor_name) {
                instance.rows.push(parsed_ln);
            }
        }
    }

    println!("\nParser Finished Successfully");
}
